<template>
  <view class="maintain-records" style="background: rgba(245, 245, 245, 0.5)">
    <!-- 维修结果部分 -->
    <view class="section">
      <view class="section-title">维修结果</view>
      <view class="section-content">
        <view wx:for="{{repairResultFields}}" wx:key="key">
          <label-field
            wx:if="{{item.show !== false}}"
            labelText="{{item.label}}"
            value="{{item.value}}"
          />
        </view>
      </view>
    </view>

    <!-- 费用明细部分 -->
    <view class="section">
      <view class="section-title">费用明细</view>
      <view class="section-content">
        <view wx:for="{{costDetailsList}}" wx:key="id" class="cost-item">
          <label-field labelText="{{item.mainLabel}}" value="{{item.cost}}">
            <view class="cost-details">
              <view wx:for="{{item.subFields}}" wx:key="key">
                <view class="label">{{ item.label }}</view>
              </view>
            </view>
          </label-field>
        </view>
      </view>
    </view>

    <!-- 更换配件记录部分 -->
    <view class="section" wx:if="{{partsRecordsList.length > 0}}">
      <view class="section-title">更换配件记录</view>
      <view class="section-content">
        <view wx:for="{{partsRecordsList}}" wx:key="id" class="parts-item">
          <label-field labelText="配件更换（元）" value="{{item.cost}}">
            <view class="parts-details">
              <view wx:for="{{item.subFields}}" wx:key="key">
                <label-field
                  labelText="{{item.label}}"
                  value="{{item.value}}"
                  showDivider="{{false}}"
                />
              </view>
            </view>
          </label-field>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { CostTypeNameMap } from 'shared/utils/constant';

  const app = getApp();
  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      maintainData: {} as any
    },
    computed: {
      // 维修结果字段配置
      repairResultFields() {
        return [
          {
            key: 'source',
            label: '工单来源',
            value: this.maintainData.source || '-'
          },
          {
            key: 'hardwareType',
            label: '故障硬件类型',
            value: this.maintainData.requireHardwareTypeName || '-'
          },
          {
            key: 'resultRemark',
            label: '结果描述',
            value: this.maintainData.maintenanceResultRemark || '-'
          },
          {
            key: 'accessoryOrder',
            label: '配件邮寄运单号',
            value: this.maintainData.accessoryOrderNo || '-',
            show: !!this.maintainData.needAccessory
          },
          {
            key: 'oldPartOrder',
            label: '旧件邮寄运单号',
            value: this.maintainData.oldPartOrderNo || '-',
            show: !!this.maintainData.needOldPart
          }
        ];
      },

      // 费用明细列表
      costDetailsList() {
        const result: any[] = [];

        // 工时费
        this.maintainData.laborCostList?.forEach((item: any, index: number) => {
          result.push({
            id: `labor_${item.id}`,
            mainLabel: '工时费（元）',
            cost: item.cost,
            subFields: [
              {
                key: 'serviceProject',
                label: '服务项',
                value: item.serviceProject || '-'
              },
              {
                key: 'costTypeRemark',
                label:
                  CostTypeNameMap[
                    item.costType as keyof typeof CostTypeNameMap
                  ] ||
                  item.costType ||
                  '-',
                value: item.remark || '-'
              }
            ],
            showDivider: true
          });
        });

        // 服务费
        this.maintainData.serviceCostList?.forEach((item: any, index: number) => {
          result.push({
            id: `service_${item.id}`,
            mainLabel: '服务费（元）',
            cost: item.cost,
            subFields: [
              {
                key: 'serviceProject',
                label: item.serviceProject || '-'
              },
              {
                key: 'costTypeRemark',
                label: `${
                  CostTypeNameMap[
                    item.costType as keyof typeof CostTypeNameMap
                  ] ||
                  item.costType ||
                  '-'
                }-${item.remark || '-'}`
              }
            ],
            showDivider: true
          });
        });

        // 其他费用
        this.maintainData.otherCostList?.forEach((item: any, index: number) => {
          const isLast = index === this.maintainData.otherCostList.length - 1;
          const hasPartsRecords =
            this.maintainData.requireHardwareModelInfoList?.length > 0;

          result.push({
            id: `other_${item.id}`,
            mainLabel: '其他费用（元）',
            cost: item.cost,
            subFields: [
              {
                key: 'serviceProject',
                label: '服务项',
                value: item.serviceProject || '-'
              },
              {
                key: 'costTypeRemark',
                label:
                  CostTypeNameMap[
                    item.costType as keyof typeof CostTypeNameMap
                  ] ||
                  item.costType ||
                  '-',
                value: item.remark || '-'
              }
            ],
            showDivider: !isLast || hasPartsRecords
          });
        });
        return result;
      },

      // 更换配件记录列表
      partsRecordsList() {
        return (
          this.maintainData.requireHardwareModelInfoList?.map(
            (item: any, index: number) => {
              const isLast =
                index ===
                this.maintainData.requireHardwareModelInfoList.length - 1;
              return {
                id: item.id,
                cost: item.newHardwareModelCost,
                subFields: [
                  {
                    key: 'oldPart',
                    label: '更换前配件',
                    value: item.oldHardwareModelName || '-'
                  },
                  {
                    key: 'newPart',
                    label: '更换后配件',
                    value: item.newHardwareModelName || '-'
                  },
                  {
                    key: 'serialNumber',
                    label: '更换后配件序列号',
                    value: item.newHardwareModelNumber || '-'
                  },
                  {
                    key: 'count',
                    label: '更换数量',
                    value: item.newHardwareModelCount || '-'
                  },
                  {
                    key: 'costTypeRemark',
                    label:
                      CostTypeNameMap[
                        item.costType as keyof typeof CostTypeNameMap
                      ] ||
                      item.costType ||
                      '-',
                    value: item.remark || '-'
                  }
                ],
                showDivider: !isLast
              };
            }
          ) || []
        );
      }
    },

    methods: {
      async loadMaintainData() {
        if (!this.orderNumber) return;

        try {
          const res = await this.techMaintenanceApi.getAcceptRequireInfoDetail(
            this.orderNumber
          );
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              maintainData: res.data
            });
          }
        } catch (error) {
          console.error('获取维修记录失败:', error);
        }
      },

      getOldPartInfo(item: any) {
        if (!item.oldHardwareModelName && !item.oldHardwareModelNumber) {
          return '-';
        }
        return `${item.oldHardwareModelName || ''} ${item.oldHardwareModelNumber || ''}`.trim();
      },

      getNewPartInfo(item: any) {
        if (!item.newHardwareModelName && !item.newHardwareModelNumber) {
          return '-';
        }
        return `${item.newHardwareModelName || ''} ${item.newHardwareModelNumber || ''}`.trim();
      }
    },
    observers: {
      orderNumber(newVal: string) {
        if (newVal) {
          this.loadMaintainData();
        }
      }
    },
    lifetimes: {
      created() {},
      attached() {
        if (this.orderNumber) {
          this.loadMaintainData();
        }
      },
      detached() {},
      ready() {}
    }
  });
</script>

<style lang="scss">
  .maintain-records {
    height: 100%;
    width: 100%;
    box-sizing: border-box;

    .section {
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      margin-bottom: 8px;
      padding: 12px;

      .section-title {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 12px;
      }

      .section-content {
        .cost-item,
        .parts-item {
          .cost-details,
          .parts-details {
            margin-top: 8px;
            padding-left: 12px;
          }
          .parts-details {
            border-left: 2px solid rgba(245, 245, 245, 1);
          }
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "label-field": "shared/ui/labelField.mpx",
      "t-divider": "tdesign-miniprogram/divider/divider"
    }
  }
</script>