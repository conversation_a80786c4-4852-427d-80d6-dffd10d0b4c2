<template>
  <view class="maintain-records" style="background: rgba(245, 245, 245, 0.5)">
    <!-- 维修结果部分 -->
    <view class="section">
      <view class="section-title">维修结果</view>
      <view class="section-content">
        <view wx:for="{{repairResultFields}}" wx:key="key">
          <label-field
            wx:if="{{item.show !== false}}"
            labelText="{{item.label}}"
            value="{{item.value}}"
          />
        </view>
      </view>
    </view>

    <!-- 费用明细部分 -->
    <view class="section">
      <view class="section-title">费用明细</view>
      <view class="section-content">
        <view wx:for="{{costDetailsList}}" wx:key="id" class="cost-item">
          <label-field labelText="{{item.mainLabel}}" value="{{item.cost}}" s>
            <view class="cost-details">
              <view
                wx:for="{{item.subFields}}"
                wx:key="key"
                class="detail-item"
              >
                <view class="detail-label">{{ item.label }}</view>
              </view>
            </view>
          </label-field>
        </view>
      </view>
    </view>

    <!-- 更换配件记录部分 -->
    <view class="section" wx:if="{{partsRecordsList.length > 0}}">
      <view class="section-title">更换配件记录</view>
      <view class="section-content">
        <view wx:for="{{partsRecordsList}}" wx:key="id" class="parts-item">
          <label-field labelText="配件更换（元）" value="{{item.cost}}">
            <view class="parts-details">
              <view
                wx:for="{{item.subFields}}"
                wx:key="key"
                class="detail-item"
              >
                <view class="detail-label">{{ item.label }}</view>
              </view>
            </view>
          </label-field>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { CostTypeNameMap } from 'shared/utils/constant';

  const app = getApp();
  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      maintainData: {} as any
    },
    computed: {
      // 维修结果字段配置
      repairResultFields() {
        return [
          {
            key: 'source',
            label: '工单来源',
            value: this.maintainData.source || '-'
          },
          {
            key: 'hardwareType',
            label: '故障硬件类型',
            value: this.maintainData.requireHardwareTypeName || '-'
          },
          {
            key: 'resultRemark',
            label: '结果描述',
            value: this.maintainData.maintenanceResultRemark || '-'
          },
          {
            key: 'accessoryOrder',
            label: '配件邮寄运单号',
            value: this.maintainData.accessoryOrderNo || '-',
            show: !!this.maintainData.needAccessory
          },
          {
            key: 'oldPartOrder',
            label: '旧件邮寄运单号',
            value: this.maintainData.oldPartOrderNo || '-',
            show: !!this.maintainData.needOldPart
          }
        ];
      },
      // 费用明细列表
      costDetailsList() {
        const result: any[] = [];
        const costTypes = [
          {
            list: this.maintainData.laborCostList,
            prefix: 'labor',
            label: '工时费（元）'
          },
          {
            list: this.maintainData.serviceCostList,
            prefix: 'service',
            label: '服务费（元）'
          },
          {
            list: this.maintainData.otherCostList,
            prefix: 'other',
            label: '其他费用（元）'
          }
        ];
        costTypes.forEach(({ list, prefix, label }) => {
          list?.forEach((item: any, index: number) => {
            const isOtherCostLast =
              prefix === 'other' && index === list.length - 1;
            const hasPartsRecords =
              this.maintainData.requireHardwareModelInfoList?.length > 0;

            result.push({
              id: `${prefix}_${item.id}`,
              mainLabel: label,
              cost: item.cost,
              subFields: [
                {
                  key: 'serviceProject',
                  label: item.serviceProject || '-'
                },
                {
                  key: 'costTypeRemark',
                  label: `${
                    CostTypeNameMap[
                      item.costType as keyof typeof CostTypeNameMap
                    ] ||
                    item.costType ||
                    '-'
                  }-${item.remark || '-'}`
                }
              ],
              showDivider:
                prefix !== 'other' || !isOtherCostLast || hasPartsRecords
            });
          });
        });

        return result;
      },
      // 更换配件记录列表
      partsRecordsList() {
        return (
          this.maintainData.requireHardwareModelInfoList?.map(
            (item: any, index: number) => {
              const isLast =
                index ===
                this.maintainData.requireHardwareModelInfoList.length - 1;
              return {
                id: item.id,
                cost: item.newHardwareModelCost,
                subFields: [
                  {
                    key: 'oldPart',
                    label: item.oldHardwareModelName || '-'
                  },
                  {
                    key: 'newPart',
                    label: item.newHardwareModelName || '-'
                  },
                  {
                    key: 'serialNumber',
                    label: item.newHardwareModelNumber || '-'
                  },
                  {
                    key: 'count',
                    label: item.newHardwareModelCount || '-'
                  },
                  {
                    key: 'costTypeRemark',
                    label: `${
                      CostTypeNameMap[
                        item.costType as keyof typeof CostTypeNameMap
                      ] ||
                      item.costType ||
                      '-'
                    }-${item.remark || '-'}`
                  }
                ],
                showDivider: !isLast
              };
            }
          ) || []
        );
      }
    },

    methods: {
      async loadMaintainData() {
        if (!this.orderNumber) return;
        try {
          const res = await this.techMaintenanceApi.getAcceptRequireInfoDetail(
            this.orderNumber
          );
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              maintainData: res.data
            });
          }
        } catch (error) {
          console.error('获取维修记录失败:', error);
        }
      }
    },
    observers: {
      orderNumber(newVal: string) {
        if (newVal) {
          this.loadMaintainData();
        }
      }
    },
    lifetimes: {
      created() {},
      attached() {
        if (this.orderNumber) {
          this.loadMaintainData();
        }
      },
      detached() {},
      ready() {}
    }
  });
</script>

<style lang="scss">
  .maintain-records {
    height: 100%;
    width: 100%;
    box-sizing: border-box;

    .section {
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      margin-bottom: 8px;
      padding: 12px;

      .section-title {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 12px;
      }

      .section-content {
        .cost-item,
        .parts-item {
          .cost-details,
          .parts-details {
            margin-top: 8px;
            padding-left: 12px;
            border-left: 2px solid rgba(245, 245, 245, 1);

            .detail-item {
              margin-bottom: 8px;

              .detail-label {
                font-size: 14px;
                font-family: PingFang SC;
                font-weight: normal;
                color: rgba(51, 51, 51, 1);
                line-height: 20px;
              }
            }
          }
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "label-field": "shared/ui/labelField.mpx",
      "t-divider": "tdesign-miniprogram/divider/divider"
    }
  }
</script>