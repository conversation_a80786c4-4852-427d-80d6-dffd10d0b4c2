<template>
  <view class="maintain-records" style="background: rgba(245, 245, 245, 0.5)">
    <!-- 维修结果部分 -->
    <view class="section">
      <view class="section-title">维修结果</view>
      <view class="section-content">
        <label-field
          labelText="工单来源"
          value="{{maintainData.source || '-'}}"
          type="inline"
          showDivider="{{true}}"
        />
        <label-field
          labelText="故障硬件类型"
          value="{{hardwareTypeNames || '-'}}"
          type="inline"
          showDivider="{{true}}"
        />
        <label-field
          labelText="结果描述"
          value="{{maintainData.maintenanceResultRemark || '-'}}"
          type="vertical"
          showDivider="{{maintainData.needAccessory || maintainData.needOldPart}}"
        />
        <label-field
          wx:if="{{maintainData.needAccessory}}"
          labelText="配件邮寄运单号"
          value="{{maintainData.accessoryOrderNo || '-'}}"
          type="inline"
          showDivider="{{maintainData.needOldPart}}"
        />
        <label-field
          wx:if="{{maintainData.needOldPart}}"
          labelText="旧件邮寄运单号"
          value="{{maintainData.oldPartOrderNo || '-'}}"
          type="inline"
          showDivider="{{false}}"
        />
      </view>
    </view>

    <!-- 费用明细部分 -->
    <view class="section">
      <view class="section-title">费用明细</view>
      <view class="section-content">
        <!-- 工时费 -->
        <view
          wx:for="{{maintainData.laborCostList}}"
          wx:key="id"
          class="cost-item"
        >
          <label-field
            labelText="工时费（元）"
            value="{{item.cost}}"
            type="inline"
            showDivider="{{false}}"
          >
            <view class="cost-details">
              <label-field
                labelText="服务项"
                value="{{item.serviceProject || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="费用承担分类"
                value="{{item.costType || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="承担方备注"
                value="{{item.remark || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
            </view>
          </label-field>
          <t-divider />
        </view>

        <!-- 服务费 -->
        <view
          wx:for="{{maintainData.serviceCostList}}"
          wx:key="id"
          class="cost-item"
        >
          <label-field
            labelText="服务费（元）"
            value="{{item.cost}}"
            type="inline"
            showDivider="{{false}}"
          >
            <view class="cost-details">
              <label-field
                labelText="服务项"
                value="{{item.serviceProject || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="费用承担分类"
                value="{{item.costType || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="承担方备注"
                value="{{item.remark || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
            </view>
          </label-field>
          <t-divider />
        </view>

        <!-- 其他费用 -->
        <view
          wx:for="{{maintainData.otherCostList}}"
          wx:key="id"
          class="cost-item"
        >
          <label-field
            labelText="其他费用（元）"
            value="{{item.cost}}"
            type="inline"
            showDivider="{{false}}"
          >
            <view class="cost-details">
              <label-field
                labelText="服务项"
                value="{{item.serviceProject || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="费用承担分类"
                value="{{item.costType || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="承担方备注"
                value="{{item.remark || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
            </view>
          </label-field>
          <t-divider
            wx:if="{{index < maintainData.otherCostList.length - 1 || maintainData.requireHardwareModelInfoList.length > 0}}"
          />
        </view>
      </view>
    </view>

    <!-- 更换配件记录部分 -->
    <view
      class="section"
      wx:if="{{maintainData.requireHardwareModelInfoList.length > 0}}"
    >
      <view class="section-title">更换配件记录</view>
      <view class="section-content">
        <view
          wx:for="{{maintainData.requireHardwareModelInfoList}}"
          wx:key="id"
          class="parts-item"
        >
          <label-field
            labelText="配件更换（元）"
            value="{{item.newHardwareModelCost}}"
            type="inline"
            showDivider="{{false}}"
          >
            <view class="parts-details">
              <label-field
                labelText="更换前配件"
                value="{{getOldPartInfo(item)}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="更换后配件"
                value="{{getNewPartInfo(item)}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="更换后配件序列号"
                value="{{item.newHardwareModelNumber || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="更换数量"
                value="{{item.newHardwareModelCount || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="配件费用承担分类"
                value="{{item.costType || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
              <label-field
                labelText="承担方备注"
                value="{{item.remark || '-'}}"
                type="inline"
                showDivider="{{false}}"
              />
            </view>
          </label-field>
          <t-divider
            wx:if="{{index < maintainData.requireHardwareModelInfoList.length - 1}}"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  const app = getApp();
  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      maintainData: {} as any,
      hardwareTypeNames: ''
    },
    methods: {
      async loadMaintainData() {
        if (!this.orderNumber) return;

        try {
          const res = await this.techMaintenanceApi.getAcceptRequireInfoDetail(
            this.orderNumber
          );
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              maintainData: res.data
            });
            this.processHardwareTypeNames();
          }
        } catch (error) {
          console.error('获取维修记录失败:', error);
        }
      },

      processHardwareTypeNames() {
        // 这里需要根据requireHardwareTypeIds处理硬件类型名称
        // 暂时使用简单的映射，实际项目中可能需要调用其他接口获取名称
        const typeMap: { [key: number]: string } = {
          1: '激光雷达',
          2: '摄像头',
          3: '超声波传感器',
          4: '控制器',
          5: '电池模组'
        };

        const names =
          this.maintainData.requireHardwareTypeIds
            ?.map((id: number) => typeMap[id] || `类型${id}`)
            .join('、') || '';
        this.setData({
          hardwareTypeNames: names
        });
      },

      getOldPartInfo(item: any) {
        if (!item.oldHardwareModelName && !item.oldHardwareModelNumber) {
          return '-';
        }
        return `${item.oldHardwareModelName || ''} ${item.oldHardwareModelNumber || ''}`.trim();
      },

      getNewPartInfo(item: any) {
        if (!item.newHardwareModelName && !item.newHardwareModelNumber) {
          return '-';
        }
        return `${item.newHardwareModelName || ''} ${item.newHardwareModelNumber || ''}`.trim();
      }
    },
    observers: {
      orderNumber(newVal: string) {
        if (newVal) {
          this.loadMaintainData();
        }
      }
    },
    lifetimes: {
      created() {},
      attached() {
        if (this.orderNumber) {
          this.loadMaintainData();
        }
      },
      detached() {},
      ready() {}
    }
  });
</script>

<style lang="scss">
  .maintain-records {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 16px;

    .section {
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      margin-bottom: 8px;
      padding: 12px;

      .section-title {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 12px;
      }

      .section-content {
        .cost-item,
        .parts-item {
          .cost-details,
          .parts-details {
            margin-top: 8px;
            padding-left: 12px;
            border-left: 2px solid rgba(245, 245, 245, 1);
          }
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "label-field": "/shared/ui/labelField.mpx",
      "t-divider": "tdesign-miniprogram/divider/divider"
    }
  }
</script>