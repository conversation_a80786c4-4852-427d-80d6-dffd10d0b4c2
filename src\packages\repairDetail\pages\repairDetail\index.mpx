<template>
  <view class="repair-detail-container">
    <!-- 导航栏 -->
    <navi-bar
      title="维修单详情"
      showReturn="{{true}}"
      backUrl="{{backUrl}}"
      showAddress="{{false}}"
      backgroundColor="transparent"
    ></navi-bar>

    <!-- 页面内容 -->
    <view class="header-section">
      <view class="status-info">
        <view class="repair-status">
          <text class="status-text">{{ requireStatusName }}</text>
          <view wx:if="{{isNewFault}}" class="fault-badge">新增故障</view>
        </view>
        <view class="schedule-time">
          <view class="time-label">预约维修时间</view>
          <view class="time-info">
            <image src="{{TechMaintainUrl.Time}}" class="time-icon" />
            <view class="time-value">{{ appointmentTime }}</view>
          </view>
        </view>
      </view>
      <image src="{{TechMaintainUrl.Car}}" class="truck-image" />
    </view>

    <!-- 白色背景信息栏 -->
    <view class="order-info-section">
      <!-- 单号栏 -->
      <view class="order-info-header">
        <view class="order-number-section">
          <view class="order-label">单号：</view>
          <view class="order-number">{{ orderNumber }}</view>
          <copy-icon code="{{orderNumber}}"></copy-icon
        ></view>
        <view>历史维修单</view>
      </view>

      <!-- Tab导航 -->
      <view class="tab-container">
        <t-tabs
          class="detail-tabs"
          value="{{activeTabKey}}"
          space-evenly="{{true}}"
          bind:change="onTabsChange"
        >
          <t-tab-panel
            wx:for="{{tabsList}}"
            wx:key="key"
            label="{{item.name}}"
            value="{{item.key}}"
          />
        </t-tabs>
      </view>

      <!-- Tab内容区域 -->
      <view
        class="tab-content-wrapper"
        style="background: {{activeTabKey === 'orderInfo' ? '#fff' : 'rgba(245, 245, 245, 0.5)'}}"
      >
        <scroll-view
          scroll-y
          type="list"
          show-scrollbar="{{true}}"
          class="scroll-container"
          style="height: calc(100vh - {{naviHeight}}px - {{tabsHeight}}px - 16px);"
        >
          <!-- 动态渲染Tab内容 -->
            <component
              is="{{currentTabComponent}}"
              wx:if="{{currentTabComponent}}"
              repairDetail="{{repairDetail}}"
              orderNumber="{{orderNumber}}"
              commentsList="{{commentsList}}"
              loading="{{loadingComments}}"
            />
        </scroll-view>
      </view>
    </view>
    </view> <!-- 页面内容结束 -->
  </view>
</template>

<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';
  import { sendGlobalEvent } from 'shared/utils/emit';

  interface RepairDetail {
    number: string;
    requireStatus: number;
    requireStatusName: string;
    deviceName: string;
    serialNo: string;
    stationName: string;
    appointmentRepairTime: string;
    isInfluenceOperation: number;
    description: string;
    pictureList: string[];
    video: string;
    reportName: string;
    reportPhone: string;
    reportEmail: string;
    stationBaseAddress: string;
  }

  interface Data {
    fetchApi: TechMaintenanceApi;
    activeTabKey: string;
    orderNumber: string;
    appointmentTime: string;
    requireStatusName: string;
    isNewFault: boolean;
    backUrl: string;
    repairDetail: RepairDetail | null;
    tabs: any;
    TechMaintainUrl: any;
    commentsList: any[];
    loadingComments: boolean;
    pageLoading: boolean;
    pageStartTime?: number;
    naviHeight: number;
    tabsHeight: number;
  }

  createPage<Data>({
    data: {
      fetchApi: new TechMaintenanceApi(),
      activeTabKey: 'repairRecords', // 默认显示诊断留言
      orderNumber: '',
      appointmentTime: '',
      requireStatusName: '',
      isNewFault: false,
      backUrl: '/pages/techMaintenance/index',
      repairDetail: null,
      TechMaintainUrl,
      commentsList: [],
      loadingComments: false,
      pageLoading: true, // 页面级别的loading状态
      naviHeight: 100,
      tabsHeight: 44,
      tabs: {
        orderInfo: { key: 'orderInfo', name: '提报信息' },
        diagnosticComments: { key: 'diagnosticComments', name: '诊断留言' },
        repairRecords: { key: 'repairRecords', name: '维修记录' },
        operationLogs: { key: 'operationLogs', name: '操作日志' }
      }
    },
    computed: {
      tabsList() {
        return Object.values(this.tabs);
      },
      currentTabComponent() {
        switch (this.activeTabKey) {
          case 'orderInfo':
            return 'order-info';
          case 'diagnosticComments':
            return 'diagnostic-comments';
          case 'repairRecords':
            return 'maintain-records';
          case 'operationLogs':
            return 'operation-log';
          default:
            return null;
        }
      }
    },
    onLoad(options: { number?: string }) {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });

      // 计算导航栏和标签栏高度
      this.calculateHeights();

      if (options.number) {
        console.log('开始加载维修单详情:', options.number);
        this.setData({
          orderNumber: options.number
        });
        sendGlobalEvent('getRepairOrderNumber', options.number);
        this.getRepairDetail(options.number);
        if (this.activeTabKey === 'diagnosticComments') {
          console.log('调接口获取留言列表');
          this.getCommentsList();
        }
      } else {
        console.error('没有收到维修单号参数');
      }
    },
    onReady() {
      wx.hideLoading();
      // 页面渲染完成，隐藏页面loading状态
      this.setData({
        pageLoading: false
      });
    },
    methods: {
      calculateHeights() {
        const systemInfo = wx.getSystemInfoSync();
        const naviHeight = systemInfo.statusBarHeight + 44; // 状态栏 + 标题栏
        this.setData({
          naviHeight,
          tabsHeight: 44 // tabs标准高度
        });
      },

      async getRepairDetail(orderNumber: string) {
        wx.showLoading({
          title: '加载中...',
          mask: true
        });
        try {
          const res = await this.fetchApi.getRequireInfoDetail(orderNumber);
          if (res.code === HTTPSTATUSCODE.Success) {
            const data = res.data;
            const attachments: any = [];
            if (data.pictureList && data.pictureList.length > 0) {
              data.pictureList.forEach((url: string, index: number) => {
                attachments.push({
                  type: 'image',
                  url,
                  locationName: `故障点${index + 1}`
                });
              });
            }
            if (data.video) {
              attachments.push({
                type: 'video',
                url: data.video,
                locationName: '故障视频'
              });
            }
            const repairDetail: any = {
              ...data,
              reporterInfo: {
                name: data.reportName || '',
                email: data.reportEmail || '',
                phone: data.reportPhone || ''
              },
              attachments
            };
            console.log('repairDetail:======', repairDetail);
            this.setData({
              repairDetail,
              appointmentTime: data.appointmentRepairTime || '',
              requireStatusName: data.requireStatusName || '',
              isNewFault: data.isInfluenceOperation === 1 // 影响运营的显示为新增故障
            });
          } else {
            wx.showToast({
              title: res.message || '获取数据失败',
              icon: 'none',
              duration: 2000
            });
            throw new Error(res.message || '获取维修单详情失败');
          }
        } catch (error) {
          console.error('获取维修单详情失败:', error);
        } finally {
          wx.hideLoading();
        }
      },

      async getCommentsList() {
        if (this.loadingComments || !this.orderNumber) return;

        this.setData({ loadingComments: true });
        wx.showLoading({
          title: '加载中...',
          mask: true
        });

        try {
          const res = await this.fetchApi.getRequireCommentList(this.orderNumber);

          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              commentsList: res.data.comments || []
            });
          } else {
            wx.showToast({
              title: res.message || '获取留言失败',
              icon: 'none',
              duration: 2000
            });
          }
        } catch (error) {
          console.error('获取留言列表失败:', error);
          wx.showToast({
            title: '获取留言失败',
            icon: 'none',
            duration: 2000
          });
        } finally {
          this.setData({ loadingComments: false });
          wx.hideLoading();
        }
      },

      onTabsChange(event: any) {
        const { value } = event.detail;
        this.setData({
          activeTabKey: value
        });

        // 当切换到诊断留言Tab时，加载留言数据
        if (value === 'diagnosticComments' && this.commentsList.length === 0) {
          this.getCommentsList();
        }
      }
    }
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/techMaintain/bg-maintaining.png?Expires=3899341225&AccessKey=n828WHAXD584pTvi&Signature=BVSz3Nggtxxx9zCwYZKLIzS%2BTDQ%3D')
      no-repeat center;
    background-size: cover;
  }

  .repair-detail-container {
    height: 100%;
    width: 100%;
    position: relative;
    box-sizing: border-box;

    .header-section {
      padding: 0 20px;
      height: 120px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      .status-info {
        flex: 1;
        padding-left: 14px;
        .repair-status {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .status-text {
            font-size: 24px;
            font-weight: 600;
            margin-right: 8px;
          }

          .fault-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
          }
        }

        .schedule-time {
          font-size: 14px;
          .time-label {
            margin-bottom: 4px;
            color: #808080;
          }
          .time-info {
            display: flex;
            align-items: center;
            .time-icon {
              width: 14px;
              height: 14px;
              vertical-align: middle;
              margin-right: 4px;
            }
            .time-value {
              color: #333333;
              font-weight: 500;
            }
          }
        }
      }
      .truck-image {
        flex: 0 0 50%;
        max-height: 100%;
        object-fit: contain;
        object-position: center;
      }
    }

    .order-info-section {
      position: relative;
      background: rgba(255, 255, 255, 1);
      border-radius: 25px;
      overflow: hidden;
      height: calc(100vh - 120px);
      display: flex;
      flex-direction: column;

      .order-info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 14px 16px;
        
        .order-number-section {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .order-label {
            font-size: 12px;
            color: #808080;
            margin-right: 5px;
          }

          .order-number {
            font-size: 14px;
            color: #333333;
            font-weight: 500;
          }
        }
      }

      .tab-container {
        border-bottom: 1px solid #f0f0f0;
        flex-shrink: 0;

        .detail-tabs {
          --td-tab-item-active-color: rgba(250, 44, 25, 1);
        }
      }

      .tab-content-wrapper {
        flex: 1;
        position: relative;
        padding: 0 14px;
        overflow: hidden;

        .scroll-container {
          height: 100%;
          width: 100%;
          box-sizing: border-box;
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "shared/ui/naviBar.mpx",
      "copy-icon": "shared/ui/copyIcon.mpx",
      "page-loading": "shared/ui/pageLoading.mpx",
      "order-info": "../../components/orderInfo.mpx",
      "diagnostic-comments": "../../components/diagnosticComments.mpx",
      "maintain-records": "../../components/maintainRecords.mpx",
      "operation-log": "../../components/operationLog.mpx",
      "t-tabs": "tdesign-miniprogram/tabs/tabs",
      "t-tab-panel": "tdesign-miniprogram/tab-panel/tab-panel"
    },
    "navigationStyle": "custom",
    "renderer": "webview"
  }
</script>