<template>
  <view class="label-field-wrapper">
    <view class="label-field-item {{type}}">
      <text class="label-field-title">{{ labelText }}</text>
      <text class="label-field-value">{{ value || '--' }}</text>
    </view>
    <slot></slot>
    <t-divider wx:if="{{showDivider}}" />
  </view>
</template>
<script>
  import { createComponent } from '@mpxjs/core';

  createComponent({
    properties: {
      labelText: {
        type: String,
        value: ''
      },
      value: {
        type: String | null,
        value: ''
      },
      type: {
        type: 'inline' | 'vertical',
        value: 'inline'
      },
      showDivider: {
        type: Boolean,
        value: false
      }
    }
  });
</script>
<style lang="scss" scoped>
  .label-field-wrapper {
    .label-field-item {
      .label-field-title {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(153, 153, 153, 1);
        margin-right: 8px;
      }
      .label-field-value {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(51, 51, 51, 1);
        word-break: break-all;
      }
      &.inline {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      &.vertical {
        display: flex;
        flex-flow: column;
        .label-field-title {
          margin-bottom: 8px;
        }
      }
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-divider': 'tdesign-miniprogram/divider/divider'
    }
  };
</script>
