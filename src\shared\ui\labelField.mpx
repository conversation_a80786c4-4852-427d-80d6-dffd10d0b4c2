<template>
  <view class="label-field-wrapper">
    <view class="label-field-item {{type}}">
      <text class="label-field-title">{{ labelText }}</text>
      <text class="label-field-value">{{ value || '--' }}</text>
    </view>
    <slot></slot>
    <t-divider wx:if="{{showDivider}}" />
  </view>
</template>
<script>
  import { createComponent } from '@mpxjs/core';

  createComponent({
    properties: {
      labelText: {
        type: String,
        value: ''
      },
      value: {
        type: String | null,
        value: ''
      },
      type: {
        type: 'inline' | 'vertical',
        value: 'inline'
      },
      showDivider: {
        type: Boolean,
        value: true
      }
    }
  });
</script>
<style lang="scss" scoped>
  .label-field-wrapper {
    .label-field-item {
      .label-field-title {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(153, 153, 153, 1);
        margin-right: 8px;
      }
      .label-field-value {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(51, 51, 51, 1);
        word-break: break-all;
        word-wrap: break-word;
        white-space: pre-wrap;
        line-height: 20px;
        max-width: 100%;
        overflow-wrap: break-word;
      }
      &.inline {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .label-field-title {
          flex-shrink: 0;
          margin-right: 8px;
          margin-top: 2px; // 与文本顶部对齐
        }

        .label-field-value {
          flex: 1;
          text-align: right;
          min-width: 0; // 允许文本收缩
        }
      }
      &.vertical {
        display: flex;
        flex-flow: column;
        .label-field-title {
          margin-bottom: 8px;
        }
      }
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      't-divider': 'tdesign-miniprogram/divider/divider'
    }
  };
</script>
